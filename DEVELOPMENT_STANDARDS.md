# 通用开发规范

## 📋 目录

### 核心规范
- [🎯 核心原则](#🎯-核心原则)
- [🔧 配置管理规范](#🔧-配置管理规范)
- [🌐 API接口规范](#🌐-api接口规范)

### 技术规范
- [🐍 后端开发规范](#🐍-后端开发规范)
- [🎨 前端开发规范](#🎨-前端开发规范)
- [🗄️ 数据库规范](#🗄️-数据库规范)
- [🌍 多语言项目规范](#🌍-多语言项目规范)

### 环境与测试
- [🔒 环境隔离规范](#🔒-环境隔离规范)
- [🧪 测试规范](#🧪-测试规范)
- [🚀 部署配置规范](#🚀-部署配置规范)

### 质量保证
- [✅ 代码质量标准](#✅-代码质量标准)
- [🛡️ 安全规范](#🛡️-安全规范)
- [📊 监控和日志](#📊-监控和日志)

### 流程管理
- [🤝 开发流程](#🤝-开发流程)
- [🔄 版本控制规范](#🔄-版本控制规范)
- [📚 项目文档结构](#📚-项目文档结构)

### 实施指南
- [🔧 配置管理最佳实践](#🔧-配置管理最佳实践)
- [📋 配置检查清单](#📋-配置检查清单)
- [🚨 常见违规示例](#🚨-常见违规示例)
- [✅ 正确配置示例](#✅-正确配置示例)

### 支持资源
- [🔧 故障排除指南](#🔧-故障排除指南)
- [📁 推荐项目结构](#📁-推荐项目结构)
- [📚 相关资源](#📚-相关资源)
- [📞 支持与反馈](#📞-支持与反馈)

---

## 🎯 核心原则

本规范适用于所有软件开发项目，旨在确保代码质量、可维护性和团队协作效率。

### 基本原则
1. **配置外部化** - 所有配置信息必须外部化，严禁硬编码
2. **环境隔离** - 开发环境必须与系统环境隔离
3. **资源清理** - 临时文件和测试数据必须及时清理
4. **安全优先** - 敏感信息必须加密存储和传输
5. **文档完整** - 代码变更必须同步更新文档
6. **测试覆盖** - 核心功能必须有完整的测试覆盖

---

## 🔧 配置管理规范

### ⚠️ 核心原则：禁止硬编码配置信息

**所有配置信息必须通过配置文件或环境变量管理，严禁在代码中硬编码！**

### 1. 配置分类

#### 环境相关配置
- API服务地址
- 数据库连接信息
- 第三方服务地址
- 文件存储路径

#### 业务相关配置
- 功能开关
- 业务参数
- 限制阈值
- 默认值设置

### 2. 配置管理方式

#### ✅ 正确的配置方式
```python
# 使用环境变量
import os
API_BASE_URL = os.environ.get('API_BASE_URL', 'http://localhost:8000')
DATABASE_URL = os.environ.get('DATABASE_URL')

# 使用配置文件
from config import settings
api_url = settings.API_BASE_URL
```

```javascript
// 前端配置文件
const config = {
  development: {
    API_BASE_URL: process.env.VUE_APP_API_URL || 'http://localhost:8000'
  },
  production: {
    API_BASE_URL: process.env.VUE_APP_API_URL || 'https://api.example.com'
  }
}
```

#### ❌ 错误的硬编码方式
```python
# 禁止这样做！
API_BASE_URL = 'http://localhost:8000'
DATABASE_HOST = '*************'
SECRET_KEY = 'hardcoded-secret-key'
```

```javascript
// 禁止这样做！
const apiUrl = 'http://localhost:8000/api/v1/'
const uploadPath = 'C:/uploads/'
```

### 3. 配置文件结构

#### 推荐的配置文件组织
```
project/
├── config/
│   ├── base.py/js          # 基础配置
│   ├── development.py/js   # 开发环境
│   ├── testing.py/js       # 测试环境
│   └── production.py/js    # 生产环境
├── .env.example           # 环境变量模板
├── .env.local            # 本地环境变量（不提交）
└── .gitignore            # 版本控制忽略文件
```

### 4. 配置验证

#### 配置完整性检查
```python
# 配置验证示例
import os
import sys

def validate_config():
    """验证必需的配置项"""
    required_vars = [
        'SECRET_KEY',
        'DATABASE_URL',
        'API_BASE_URL'
    ]

    missing_vars = []
    for var in required_vars:
        if not os.environ.get(var):
            missing_vars.append(var)

    if missing_vars:
        print(f"错误：缺少必需的环境变量: {', '.join(missing_vars)}")
        sys.exit(1)

    print("✅ 配置验证通过")

# 在应用启动时调用
if __name__ == '__main__':
    validate_config()
```

---

## 🌐 API接口规范

### 1. API地址管理

#### ✅ 正确的API地址配置
```python
# 后端配置
class APIConfig:
    BASE_URL = os.environ.get('API_BASE_URL', 'http://localhost:8000')
    VERSION = os.environ.get('API_VERSION', 'v1')

    @property
    def full_url(self):
        return f"{self.BASE_URL}/api/{self.VERSION}/"
```

```javascript
// 前端配置
export const apiConfig = {
  baseURL: process.env.REACT_APP_API_URL || 'http://localhost:8000',
  version: 'v1',
  timeout: 10000
}

export const buildApiUrl = (endpoint) => {
  return `${apiConfig.baseURL}/api/${apiConfig.version}${endpoint}`
}
```

### 2. RESTful API设计规范

#### 标准HTTP方法使用
```
GET    /api/v1/resources/          # 获取资源列表
POST   /api/v1/resources/          # 创建新资源
GET    /api/v1/resources/{id}/     # 获取特定资源
PUT    /api/v1/resources/{id}/     # 更新资源
DELETE /api/v1/resources/{id}/     # 删除资源
```

#### 统一响应格式
```json
{
  "success": true,
  "data": {},
  "message": "操作成功",
  "code": 200,
  "timestamp": "2025-07-29T10:30:00Z"
}
```

---

## 🐍 后端开发规范

### 1. 项目结构规范

#### 推荐的后端项目结构
```
backend/
├── config/               # 配置文件目录
│   ├── settings/        # 分环境配置
│   │   ├── base.py     # 基础配置
│   │   ├── development.py # 开发环境
│   │   ├── testing.py  # 测试环境
│   │   └── production.py # 生产环境
│   └── urls.py
├── apps/                # 应用模块
├── utils/               # 工具类
├── requirements/        # 依赖管理
└── manage.py
```

### 2. 配置管理规范

#### 环境变量使用
```python
import os

# ✅ 正确：从环境变量读取配置
SECRET_KEY = os.environ.get('SECRET_KEY')
DEBUG = os.environ.get('DEBUG', 'False').lower() == 'true'
DATABASE_URL = os.environ.get('DATABASE_URL')

# 数据库配置
DATABASES = {
    'default': {
        'ENGINE': os.environ.get('DB_ENGINE', 'django.db.backends.sqlite3'),
        'NAME': os.environ.get('DB_NAME', 'db.sqlite3'),
        'USER': os.environ.get('DB_USER', ''),
        'PASSWORD': os.environ.get('DB_PASSWORD', ''),
        'HOST': os.environ.get('DB_HOST', ''),
        'PORT': os.environ.get('DB_PORT', ''),
    }
}

# ❌ 错误：硬编码敏感信息
# SECRET_KEY = 'hardcoded-secret-key'
# DEBUG = True
# DATABASE_PASSWORD = 'password123'
```

### 3. API视图规范

#### 标准API响应格式
```python
from rest_framework import viewsets, status
from rest_framework.response import Response

class BaseViewSet(viewsets.ModelViewSet):
    """基础ViewSet，提供统一的响应格式"""

    def create(self, request):
        try:
            # 业务逻辑处理
            serializer = self.get_serializer(data=request.data)
            serializer.is_valid(raise_exception=True)
            instance = serializer.save()

            return Response({
                'success': True,
                'data': serializer.data,
                'message': '创建成功'
            }, status=status.HTTP_201_CREATED)

        except Exception as e:
            return Response({
                'success': False,
                'message': str(e),
                'data': None
            }, status=status.HTTP_400_BAD_REQUEST)
```

---

## 🎨 前端开发规范

### 1. 项目结构规范

#### 推荐的前端项目结构
```
frontend/
├── src/                 # 源代码目录
│   ├── pages/          # 页面文件
│   ├── components/     # 公共组件
│   ├── utils/          # 工具类
│   ├── config/         # 配置文件
│   ├── assets/         # 静态资源
│   └── api/            # API接口定义
├── public/             # 公共资源
└── package.json        # 依赖配置
```

### 2. 配置管理规范

#### 前端环境配置
```javascript
// config/index.js
const config = {
  development: {
    API_BASE_URL: process.env.VUE_APP_API_URL || 'http://localhost:8000',
    API_TIMEOUT: 10000,
    DEBUG: true
  },
  production: {
    API_BASE_URL: process.env.VUE_APP_API_URL || 'https://api.example.com',
    API_TIMEOUT: 5000,
    DEBUG: false
  }
}

export default config[process.env.NODE_ENV || 'development']
```

#### API工具类
```javascript
// utils/api.js
import config from '@/config'

export const buildApiUrl = (endpoint) => {
  return `${config.API_BASE_URL}/api/v1${endpoint}`
}

export const apiRequest = async (url, options = {}) => {
  try {
    const response = await fetch(buildApiUrl(url), {
      timeout: config.API_TIMEOUT,
      ...options
    })
    return await response.json()
  } catch (error) {
    console.error('API请求失败:', error)
    throw error
  }
}
```

### 3. 组件开发规范

#### 标准组件结构
```vue
<template>
  <div class="component-container">
    <!-- 组件内容 -->
  </div>
</template>

<script>
import { buildApiUrl } from '@/utils/api'

export default {
  name: 'ComponentName',

  props: {
    // 属性定义
  },

  data() {
    return {
      loading: false,
      data: null
    }
  },

  methods: {
    async fetchData() {
      this.loading = true
      try {
        const url = buildApiUrl('/endpoint/')
        const response = await this.$http.get(url)
        this.data = response.data
      } catch (error) {
        this.handleError(error)
      } finally {
        this.loading = false
      }
    },

    handleError(error) {
      console.error('操作失败:', error)
      // 统一错误处理
    }
  }
}
</script>

<style scoped>
.component-container {
  /* 样式定义 */
}
</style>
```

---

## 🗄️ 数据库规范

### 1. 数据库配置管理

#### ✅ 正确的数据库配置
```python
import os

# 使用环境变量配置数据库
DATABASES = {
    'default': {
        'ENGINE': os.environ.get('DB_ENGINE', 'django.db.backends.sqlite3'),
        'NAME': os.environ.get('DB_NAME', 'db.sqlite3'),
        'USER': os.environ.get('DB_USER', ''),
        'PASSWORD': os.environ.get('DB_PASSWORD', ''),
        'HOST': os.environ.get('DB_HOST', ''),
        'PORT': os.environ.get('DB_PORT', ''),
        'OPTIONS': {
            'charset': 'utf8mb4',
        }
    }
}

# ❌ 错误：硬编码数据库配置
# DATABASES = {
#     'default': {
#         'ENGINE': 'django.db.backends.mysql',
#         'NAME': 'myproject',
#         'USER': 'root',
#         'PASSWORD': 'password123',
#         'HOST': '*************',
#         'PORT': '3306',
#     }
# }
```

### 2. 缓存配置管理

#### Redis配置示例
```python
# 使用环境变量配置Redis
CACHES = {
    'default': {
        'BACKEND': 'django_redis.cache.RedisCache',
        'LOCATION': os.environ.get('REDIS_URL', 'redis://localhost:6379/1'),
        'OPTIONS': {
            'CLIENT_CLASS': 'django_redis.client.DefaultClient',
            'PASSWORD': os.environ.get('REDIS_PASSWORD', ''),
        }
    }
}
```

### 3. 模型设计规范

#### 基础模型设计
```python
from django.db import models
from django.utils import timezone

class BaseModel(models.Model):
    """基础模型，提供通用字段"""
    created_at = models.DateTimeField('创建时间', default=timezone.now)
    updated_at = models.DateTimeField('更新时间', auto_now=True)
    is_active = models.BooleanField('是否激活', default=True)

    class Meta:
        abstract = True

class TimestampedModel(models.Model):
    """时间戳模型"""
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        abstract = True
```

---

## 🔒 环境隔离规范

### ⚠️ 核心原则：严格隔离开发环境

**所有Python包必须安装在虚拟环境中，严禁污染系统环境！**

### 1. Python虚拟环境管理

#### ✅ 正确的虚拟环境使用

##### 使用venv创建虚拟环境
```bash
# 创建虚拟环境
python -m venv venv

# 激活虚拟环境
# Windows
venv\Scripts\activate
# Linux/macOS
source venv/bin/activate

# 安装依赖
pip install -r requirements.txt

# 退出虚拟环境
deactivate
```

##### 使用conda创建虚拟环境
```bash
# 创建虚拟环境
conda create -n project_name python=3.9

# 激活虚拟环境
conda activate project_name

# 安装依赖
pip install -r requirements.txt
# 或使用conda
conda install package_name

# 退出虚拟环境
conda deactivate
```

##### 使用pipenv管理环境
```bash
# 创建虚拟环境并安装依赖
pipenv install

# 激活虚拟环境
pipenv shell

# 安装新包
pipenv install package_name

# 安装开发依赖
pipenv install package_name --dev
```

#### ❌ 错误的包安装方式
```bash
# 禁止直接在系统环境安装包！
pip install django
sudo pip install requests
pip install --user package_name
```

### 2. Node.js环境管理

#### 使用nvm管理Node.js版本
```bash
# 安装指定版本的Node.js
nvm install 16.20.0
nvm use 16.20.0

# 设置默认版本
nvm alias default 16.20.0
```

#### 项目依赖管理
```bash
# 使用npm
npm install

# 使用yarn
yarn install

# 使用pnpm
pnpm install
```

### 3. 环境文件管理

#### .gitignore配置
```gitignore
# Python虚拟环境
venv/
env/
.venv/
.env/

# Node.js
node_modules/
.npm/

# 环境配置文件
.env.local
.env.*.local

# IDE配置
.vscode/
.idea/
```

#### 虚拟环境目录结构
```
project/
├── venv/                # 虚拟环境目录（不提交）
├── requirements.txt     # Python依赖
├── requirements-dev.txt # 开发依赖
├── package.json        # Node.js依赖
├── .python-version     # Python版本指定
└── .nvmrc             # Node.js版本指定
```

---

## 🧪 测试规范

### ⚠️ 核心原则：保持项目简洁

**测试过程中产生的所有临时文件必须在测试完成后清理，保持项目目录整洁！**

### 1. 测试文件管理

#### ✅ 正确的测试文件处理

##### 测试临时文件清理
```python
import os
import tempfile
import unittest
from pathlib import Path

class TestFileOperations(unittest.TestCase):

    def setUp(self):
        """测试前准备：创建临时目录"""
        self.test_dir = tempfile.mkdtemp()
        self.temp_files = []

    def tearDown(self):
        """测试后清理：删除所有临时文件"""
        # 清理临时文件
        for file_path in self.temp_files:
            if os.path.exists(file_path):
                os.remove(file_path)

        # 清理临时目录
        import shutil
        if os.path.exists(self.test_dir):
            shutil.rmtree(self.test_dir)

    def test_file_creation(self):
        """测试文件创建"""
        test_file = os.path.join(self.test_dir, 'test.txt')
        self.temp_files.append(test_file)

        # 执行测试逻辑
        with open(test_file, 'w') as f:
            f.write('test content')

        self.assertTrue(os.path.exists(test_file))
        # 注意：不在这里删除，由tearDown统一清理
```

##### 使用pytest的临时文件管理
```python
import pytest
import tempfile

def test_file_processing(tmp_path):
    """使用pytest的tmp_path自动管理临时文件"""
    # tmp_path会在测试结束后自动清理
    test_file = tmp_path / "test.txt"
    test_file.write_text("test content")

    # 执行测试逻辑
    assert test_file.exists()
    # 无需手动清理，pytest会自动处理

@pytest.fixture
def temp_database():
    """创建临时数据库"""
    db_file = tempfile.mktemp(suffix='.db')
    # 创建数据库
    yield db_file
    # 清理数据库文件
    if os.path.exists(db_file):
        os.remove(db_file)
```

#### ❌ 错误的测试文件处理
```python
# 禁止这样做！
def test_file_creation():
    # 在项目目录创建测试文件但不清理
    with open('test_output.txt', 'w') as f:
        f.write('test')

    # 测试完成后文件仍然存在，污染项目目录
```

### 2. 测试数据管理

#### 测试数据库配置
```python
# settings/testing.py
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.sqlite3',
        'NAME': ':memory:',  # 使用内存数据库
    }
}

# 或使用临时文件数据库
import tempfile
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.sqlite3',
        'NAME': tempfile.mktemp(suffix='.db'),
    }
}
```

#### 测试文件上传处理
```python
from django.test import TestCase
from django.core.files.uploadedfile import SimpleUploadedFile
import tempfile
import os

class FileUploadTest(TestCase):

    def setUp(self):
        self.temp_files = []

    def tearDown(self):
        # 清理测试过程中创建的文件
        for file_path in self.temp_files:
            if os.path.exists(file_path):
                os.remove(file_path)

    def test_file_upload(self):
        # 创建临时测试文件
        test_file = SimpleUploadedFile(
            "test.txt",
            b"test content",
            content_type="text/plain"
        )

        # 执行上传测试
        response = self.client.post('/upload/', {'file': test_file})

        # 记录需要清理的文件
        if hasattr(response, 'uploaded_file_path'):
            self.temp_files.append(response.uploaded_file_path)
```

### 3. 测试环境配置

#### .gitignore测试相关配置
```gitignore
# 测试产生的文件
test_output/
test_results/
*.test.log
coverage.xml
.coverage
htmlcov/

# 临时测试文件
temp_test_*
test_temp_*
*.tmp
*.temp

# 测试数据库
test_*.db
test.sqlite3
```

#### 测试脚本示例
```bash
#!/bin/bash
# test.sh - 测试脚本

echo "开始运行测试..."

# 创建临时目录
TEST_TEMP_DIR=$(mktemp -d)
export TEST_TEMP_DIR

# 运行测试
python -m pytest tests/ --cov=src/

# 清理临时文件
echo "清理测试临时文件..."
rm -rf "$TEST_TEMP_DIR"
rm -f test_*.log
rm -f *.test.db

echo "测试完成，临时文件已清理"
```

---

## 🌍 多语言项目规范

### 1. 通用配置管理原则

#### 各语言环境变量读取方式
```python
# Python
import os
API_URL = os.environ.get('API_URL', 'http://localhost:8000')
```

```javascript
// JavaScript/Node.js
const API_URL = process.env.API_URL || 'http://localhost:8000';
```

```java
// Java
String apiUrl = System.getenv().getOrDefault("API_URL", "http://localhost:8000");
```

```csharp
// C#
string apiUrl = Environment.GetEnvironmentVariable("API_URL") ?? "http://localhost:8000";
```

```go
// Go
import "os"
apiUrl := os.Getenv("API_URL")
if apiUrl == "" {
    apiUrl = "http://localhost:8000"
}
```

```php
// PHP
$apiUrl = $_ENV['API_URL'] ?? 'http://localhost:8000';
```

```ruby
# Ruby
api_url = ENV['API_URL'] || 'http://localhost:8000'
```

### 2. 虚拟环境管理

#### Python
```bash
python -m venv venv
source venv/bin/activate  # Linux/macOS
venv\Scripts\activate     # Windows
```

#### Node.js
```bash
nvm use 16.20.0
npm install
```

#### Java
```bash
# 使用SDKMAN管理Java版本
sdk use java 11.0.12-open
```

#### Ruby
```bash
# 使用rbenv管理Ruby版本
rbenv local 3.0.0
bundle install
```

#### Go
```bash
# Go modules自动管理依赖
go mod tidy
```

### 3. 依赖管理文件

#### 各语言依赖文件对应关系
```
Python:     requirements.txt, Pipfile, pyproject.toml
Node.js:    package.json, yarn.lock, pnpm-lock.yaml
Java:       pom.xml, build.gradle
C#:         *.csproj, packages.config
Go:         go.mod, go.sum
PHP:        composer.json, composer.lock
Ruby:       Gemfile, Gemfile.lock
Rust:       Cargo.toml, Cargo.lock
```

---

## 🚀 部署配置规范

### 1. 环境变量管理

#### .env.example 模板文件
```bash
# 应用基础配置
APP_NAME=your_project_name
APP_ENV=development
APP_DEBUG=true
APP_URL=http://localhost

# 数据库配置
DB_CONNECTION=mysql
DB_HOST=localhost
DB_PORT=3306
DB_DATABASE=your_database
DB_USERNAME=your_username
DB_PASSWORD=your_password

# 缓存配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# API配置
API_BASE_URL=http://localhost:8000
API_VERSION=v1
API_TIMEOUT=30

# 安全配置
SECRET_KEY=your_secret_key
JWT_SECRET=your_jwt_secret
ENCRYPTION_KEY=your_encryption_key

# 第三方服务配置
MAIL_HOST=smtp.example.com
MAIL_PORT=587
MAIL_USERNAME=your_email
MAIL_PASSWORD=your_password

# 文件存储配置
STORAGE_TYPE=local
STORAGE_PATH=/path/to/storage
UPLOAD_MAX_SIZE=10485760

# 外部工具路径（根据项目需要）
EXTERNAL_TOOL_PATH=/path/to/external/tool
```

### 2. 容器化配置

#### Docker配置示例
```dockerfile
# Dockerfile
FROM node:16-alpine AS frontend
WORKDIR /app/frontend
COPY frontend/package*.json ./
RUN npm install
COPY frontend/ ./
RUN npm run build

FROM python:3.9-slim AS backend
WORKDIR /app
COPY requirements.txt ./
RUN pip install -r requirements.txt
COPY . .
COPY --from=frontend /app/frontend/dist ./static/

EXPOSE 8000
CMD ["gunicorn", "project.wsgi:application", "--bind", "0.0.0.0:8000"]
```

#### docker-compose.yml 示例
```yaml
version: '3.8'
services:
  web:
    build: .
    ports:
      - "8000:8000"
    environment:
      - DB_HOST=db
      - REDIS_HOST=redis
    depends_on:
      - db
      - redis
    env_file:
      - .env.local

  db:
    image: mysql:8.0
    environment:
      MYSQL_DATABASE: ${DB_DATABASE}
      MYSQL_USER: ${DB_USERNAME}
      MYSQL_PASSWORD: ${DB_PASSWORD}
      MYSQL_ROOT_PASSWORD: ${DB_ROOT_PASSWORD}
    volumes:
      - mysql_data:/var/lib/mysql

  redis:
    image: redis:alpine
    command: redis-server --requirepass ${REDIS_PASSWORD}

volumes:
  mysql_data:
```

---

## ✅ 代码质量标准

### 1. 代码审查清单

#### 配置管理检查
- [ ] 是否有硬编码的API地址
- [ ] 是否有硬编码的文件路径
- [ ] 是否有硬编码的数据库连接信息
- [ ] 是否正确使用了环境变量

#### 代码质量检查
- [ ] 是否添加了适当的错误处理
- [ ] 是否添加了必要的日志记录
- [ ] 是否遵循了命名规范
- [ ] 是否添加了文档注释

### 2. 单元测试规范
```python
# 配置测试示例
import unittest
import os

class ConfigurationTest(unittest.TestCase):
    def test_required_env_vars(self):
        """测试必需的环境变量"""
        required_vars = ['SECRET_KEY', 'DATABASE_URL', 'API_BASE_URL']
        for var in required_vars:
            self.assertIsNotNone(os.environ.get(var), f"环境变量 {var} 未设置")

    def test_no_hardcoded_values(self):
        """测试是否存在硬编码值"""
        # 这里可以添加检查硬编码的逻辑
        pass
```

### 3. 性能标准
- API响应时间 < 2秒
- 数据库查询优化
- 静态资源压缩
- 合理的缓存策略

### 4. 代码规范检查工具

#### Python项目
```bash
# 代码格式化
black .
isort .

# 代码质量检查
flake8 .
pylint src/

# 类型检查
mypy src/

# 安全检查
bandit -r src/
```

#### JavaScript/Node.js项目
```bash
# 代码格式化
prettier --write .
eslint --fix .

# 类型检查（TypeScript）
tsc --noEmit

# 安全检查
npm audit
yarn audit
```

### 5. 持续集成检查

#### GitHub Actions示例
```yaml
name: Code Quality Check
on: [push, pull_request]

jobs:
  quality-check:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2

      - name: Check for hardcoded values
        run: |
          # 检查硬编码API地址
          if grep -r "http://localhost" --include="*.py" --include="*.js" src/; then
            echo "发现硬编码API地址"
            exit 1
          fi

      - name: Validate environment template
        run: |
          # 检查.env.example是否存在
          if [ ! -f .env.example ]; then
            echo ".env.example文件不存在"
            exit 1
          fi
```

---

## 📚 项目文档结构

### 推荐的文档组织
```
docs/
├── README.md              # 项目概述
├── DEVELOPMENT_GUIDE.md   # 开发指南
├── API_DOCUMENTATION.md   # API接口文档
├── DEPLOYMENT_GUIDE.md    # 部署指南
├── CONFIGURATION_GUIDE.md # 配置说明
└── TROUBLESHOOTING.md     # 故障排除
```

---

## 🤝 开发流程

1. **开发前**: 检查配置文件，确保环境正确
2. **开发中**: 遵循本规范，禁止硬编码
3. **提交前**: 进行代码审查检查
4. **部署前**: 验证配置文件和环境变量

---

## 🔧 配置管理最佳实践

### 1. 配置文件层级
```
项目根目录/
├── .env.example          # 环境变量模板
├── .env.local           # 本地开发配置（不提交）
├── config/              # 配置文件目录
│   ├── settings/        # 分环境配置
│   │   ├── base.py/js   # 基础配置
│   │   ├── development.py/js # 开发环境
│   │   ├── testing.py/js # 测试环境
│   │   └── production.py/js # 生产环境
│   └── constants.py/js  # 常量定义
└── src/                 # 源代码目录
```

### 2. 配置优先级
1. 环境变量 (最高优先级)
2. .env.local 文件
3. 配置文件默认值
4. 硬编码值 (禁止使用)

### 3. 敏感信息处理
```python
# ✅ 正确：使用环境变量
SECRET_KEY = os.environ.get('SECRET_KEY')
DB_PASSWORD = os.environ.get('DB_PASSWORD')

# ❌ 错误：硬编码敏感信息
SECRET_KEY = 'django-insecure-hardcoded-key'
DB_PASSWORD = 'password123'
```

---

## 🛡️ 安全规范

### 1. API安全
- 所有API接口必须进行身份验证
- 敏感操作需要权限验证
- 输入数据必须进行验证和清理
- 使用HTTPS进行数据传输

### 2. 数据安全
- 敏感数据加密存储
- 定期备份数据库
- 实施访问控制
- 记录操作日志

### 3. 配置安全
- 生产环境关闭DEBUG模式
- 使用强密码和密钥
- 定期更新依赖包
- 限制CORS域名

---

## 📊 监控和日志

### 1. 日志规范
```python
import logging

logger = logging.getLogger(__name__)

# 不同级别的日志使用
logger.debug('调试信息')
logger.info('一般信息')
logger.warning('警告信息')
logger.error('错误信息')
logger.critical('严重错误')
```

### 2. 性能监控
- API响应时间监控
- 数据库查询性能
- 内存和CPU使用率
- 错误率统计

---

## 🔄 版本控制规范

### 1. Git提交规范
```
feat: 新功能
fix: 修复bug
docs: 文档更新
style: 代码格式调整
refactor: 代码重构
test: 测试相关
chore: 构建过程或辅助工具的变动
```

### 2. 分支管理
- `main`: 生产环境分支
- `develop`: 开发分支
- `feature/*`: 功能分支
- `hotfix/*`: 紧急修复分支

---

**最后更新**: 2025-07-29
**适用范围**: 所有软件开发项目
**强制执行**: 所有团队成员必须遵循

---

## 📋 配置检查清单

### 开发前检查
- [ ] 是否创建了 `.env.example` 模板文件
- [ ] 是否配置了 `.env.local` 本地环境文件
- [ ] 是否将 `.env.local` 添加到 `.gitignore`
- [ ] 是否设置了所有必需的环境变量
- [ ] 是否创建了Python虚拟环境
- [ ] 是否在虚拟环境中安装依赖包
- [ ] 是否配置了测试临时文件清理机制

### 代码审查检查
- [ ] 是否有硬编码的API地址
- [ ] 是否有硬编码的数据库连接信息
- [ ] 是否有硬编码的文件路径
- [ ] 是否有硬编码的密钥或密码
- [ ] 是否正确使用了配置文件或环境变量
- [ ] 是否添加了适当的错误处理
- [ ] 是否遵循了命名规范
- [ ] 是否在虚拟环境中安装了新的依赖包
- [ ] 测试代码是否包含临时文件清理逻辑
- [ ] 是否将虚拟环境目录添加到 `.gitignore`

### 部署前检查
- [ ] 生产环境是否关闭了调试模式
- [ ] 是否使用了强密钥和密码
- [ ] 是否配置了正确的域名和CORS设置
- [ ] 是否设置了适当的文件权限
- [ ] 是否配置了监控和日志
- [ ] 是否清理了所有测试临时文件
- [ ] 部署包中是否排除了虚拟环境目录
- [ ] 是否验证了生产环境的依赖包版本

---

## 🚨 常见违规示例

### ❌ 硬编码API地址
```python
# 错误示例
response = requests.get('http://localhost:8000/api/users/')
api_url = 'https://api.example.com/v1/'
```

### ❌ 硬编码数据库信息
```python
# 错误示例
DATABASE_URL = 'mysql://root:password@localhost/mydb'
REDIS_URL = 'redis://localhost:6379/0'
```

### ❌ 硬编码文件路径
```python
# 错误示例
UPLOAD_PATH = 'C:/uploads/'
LOG_FILE = '/var/log/app.log'
```

### ❌ 硬编码密钥
```python
# 错误示例
SECRET_KEY = 'django-insecure-hardcoded-key'
JWT_SECRET = 'my-jwt-secret-123'
```

### ❌ 污染系统环境
```bash
# 错误示例：直接在系统环境安装包
pip install django
sudo pip install requests
pip install --global package_name
```

### ❌ 测试文件未清理
```python
# 错误示例：测试后不清理临时文件
def test_file_processing():
    # 创建测试文件但不清理
    with open('test_output.txt', 'w') as f:
        f.write('test data')

    # 测试逻辑...
    # 文件仍然存在，污染项目目录
```

---

## ✅ 正确配置示例

### ✅ 使用环境变量
```python
import os

# 正确示例
API_BASE_URL = os.environ.get('API_BASE_URL', 'http://localhost:8000')
DATABASE_URL = os.environ.get('DATABASE_URL')
SECRET_KEY = os.environ.get('SECRET_KEY')
UPLOAD_PATH = os.environ.get('UPLOAD_PATH', './uploads/')
```

### ✅ 使用配置文件
```javascript
// config.js
export default {
  apiBaseUrl: process.env.VUE_APP_API_URL || 'http://localhost:8000',
  timeout: parseInt(process.env.VUE_APP_TIMEOUT) || 10000,
  debug: process.env.NODE_ENV === 'development'
}
```

### ✅ 正确的环境隔离
```bash
# 正确示例：使用虚拟环境
python -m venv venv
source venv/bin/activate  # Linux/macOS
# 或 venv\Scripts\activate  # Windows

pip install django
pip install -r requirements.txt
```

### ✅ 正确的测试文件管理
```python
# 正确示例：自动清理测试文件
import tempfile
import unittest

class TestFileProcessing(unittest.TestCase):
    def setUp(self):
        self.temp_dir = tempfile.mkdtemp()
        self.temp_files = []

    def tearDown(self):
        # 自动清理所有临时文件
        import shutil
        shutil.rmtree(self.temp_dir)
        for file_path in self.temp_files:
            if os.path.exists(file_path):
                os.remove(file_path)

    def test_processing(self):
        test_file = os.path.join(self.temp_dir, 'test.txt')
        # 测试逻辑...
        # 文件会在tearDown中自动清理
```

---

## 📚 相关资源

- [Twelve-Factor App](https://12factor.net/) - 应用配置最佳实践
- [Environment Variables Best Practices](https://blog.bitsrc.io/environment-variables-best-practices-6b5b9c5c8c8e)
- [Docker Configuration Management](https://docs.docker.com/compose/environment-variables/)
- [Security Best Practices](https://owasp.org/www-project-top-ten/)

---

## � 故障排除指南

### 1. 常见配置问题

#### 环境变量未生效
```bash
# 检查环境变量是否设置
echo $API_BASE_URL

# 检查.env文件是否存在
ls -la .env*

# 验证环境变量加载
python -c "import os; print(os.environ.get('API_BASE_URL'))"
```

#### 虚拟环境问题
```bash
# 检查是否在虚拟环境中
which python
which pip

# 重新创建虚拟环境
rm -rf venv
python -m venv venv
source venv/bin/activate
pip install -r requirements.txt
```

#### 依赖包冲突
```bash
# Python依赖冲突解决
pip freeze > current_packages.txt
pip uninstall -r current_packages.txt -y
pip install -r requirements.txt

# Node.js依赖冲突解决
rm -rf node_modules package-lock.json
npm install
```

### 2. 硬编码检测工具

#### 自动检测脚本
```bash
#!/bin/bash
# check_hardcoded.sh - 检测硬编码问题

echo "🔍 检查硬编码问题..."

# 检查常见硬编码模式
patterns=(
    "http://localhost"
    "127\.0\.0\.1"
    "password123"
    "secret.*key.*="
    "mysql://.*:.*@"
)

found_issues=0

for pattern in "${patterns[@]}"; do
    echo "检查模式: $pattern"
    if grep -r "$pattern" --include="*.py" --include="*.js" --include="*.java" src/ 2>/dev/null; then
        echo "❌ 发现硬编码: $pattern"
        found_issues=$((found_issues + 1))
    fi
done

if [ $found_issues -eq 0 ]; then
    echo "✅ 未发现硬编码问题"
else
    echo "❌ 发现 $found_issues 个硬编码问题，请修复"
    exit 1
fi
```

### 3. 配置验证清单

#### 开发环境检查
- [ ] `.env.example` 文件存在且完整
- [ ] `.env.local` 文件已配置
- [ ] 虚拟环境已激活
- [ ] 所有依赖包已安装
- [ ] 数据库连接正常
- [ ] API服务可访问

#### 生产环境检查
- [ ] 所有环境变量已设置
- [ ] 调试模式已关闭
- [ ] 使用强密钥和密码
- [ ] HTTPS已启用
- [ ] 日志记录正常
- [ ] 监控系统运行

---

## �📞 支持与反馈

如有疑问或建议，请：
1. 查阅相关文档
2. 咨询项目负责人
3. 提交改进建议
4. 参与规范讨论

---

## 📁 推荐项目结构

### 通用项目结构模板
```
project-name/
├── .env.example              # 环境变量模板
├── .gitignore               # 版本控制忽略文件
├── README.md                # 项目说明文档
├── DEVELOPMENT_STANDARDS.md # 开发规范文档
├── config/                  # 配置文件目录
│   ├── settings/           # 分环境配置
│   └── constants.py/js     # 常量定义
├── src/                    # 源代码目录
│   ├── api/               # API接口层
│   ├── services/          # 业务逻辑层
│   ├── models/            # 数据模型层
│   └── utils/             # 工具函数
├── tests/                  # 测试文件目录
│   ├── unit/              # 单元测试
│   ├── integration/       # 集成测试
│   └── fixtures/          # 测试数据
├── docs/                   # 项目文档
│   ├── api/               # API文档
│   ├── deployment/        # 部署文档
│   └── development/       # 开发文档
├── scripts/                # 脚本文件
│   ├── setup.sh           # 环境设置脚本
│   ├── deploy.sh          # 部署脚本
│   └── check_hardcoded.sh # 硬编码检查脚本
└── requirements.txt        # 依赖文件（Python示例）
```

### 配置文件组织原则
1. **分离关注点** - 不同类型的配置分别管理
2. **环境隔离** - 开发、测试、生产环境配置分离
3. **安全优先** - 敏感信息通过环境变量管理
4. **版本控制** - 模板文件纳入版本控制，实际配置文件排除
5. **文档完整** - 每个配置项都有清晰的说明
